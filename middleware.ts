import { NextRequest, NextResponse } from 'next/server';
import * as jose from 'jose';

const protectedRoutes = ['/app'];
const authRoutes = ['/signin', '/register'];
const publicRoutes = ['/'];

export async function middleware(req: NextRequest) {
	const pathname = req.nextUrl.pathname;
	const url = req.nextUrl.clone();

	// Debug: Log every middleware execution
	console.log(`🔒 MIDDLEWARE EXECUTING for: ${pathname}`);

	// Force redirect for testing - REMOVE THIS AFTER TESTING
	if (pathname.startsWith('/app')) {
		console.log(`🚨 FORCING REDIRECT FROM ${pathname} TO /signin`);
		url.pathname = '/signin';
		return NextResponse.redirect(url);
	}

	// Check if the current path is a protected route (requires authentication)
	const isProtectedRoute = protectedRoutes.some((route) =>
		pathname.startsWith(route),
	);

	// Check if the current path is an auth route (signin/register)
	const isAuthRoute = authRoutes.some((route) => pathname.startsWith(route));

	// Check if the current path is a public route
	const isPublicRoute = publicRoutes.some((route) => pathname === route);

	// Get token from cookies (more secure than localStorage in middleware)
	const token = req.cookies.get('auth-token')?.value;

	console.log(`Middleware check for ${pathname}:`, {
		isProtectedRoute,
		isAuthRoute,
		isPublicRoute,
		hasToken: !!token,
		tokenLength: token?.length || 0,
		protectedRoutes,
	});

	// If accessing protected route without token, redirect to signin
	if (isProtectedRoute && !token) {
		console.log(`Redirecting unauthenticated user from ${pathname} to /signin`);
		url.pathname = '/signin';
		url.searchParams.set('redirect', pathname); // Remember where they wanted to go
		return NextResponse.redirect(url);
	}

	// If we have a token, verify it
	if (token) {
		try {
			const secret = process.env.NEXT_PUBLIC_JWT_SECRET;
			if (!secret) {
				console.error('JWT_SECRET is not defined');
				if (isProtectedRoute) {
					url.pathname = '/signin';
					return NextResponse.redirect(url);
				}
				return NextResponse.next();
			}

			const { payload } = await jose.jwtVerify(
				token,
				new TextEncoder().encode(secret),
			);

			const userRole = payload.role as 'ADMIN' | 'USER';
			const userStatus = payload.status as 'ACTIVE' | 'INACTIVE' | 'REJECTED';
			const tokenType = payload.type as string;

			// Check if it's an auth token
			if (tokenType !== 'auth') {
				console.log('Invalid token type, redirecting to signin');
				url.pathname = '/signin';
				url.searchParams.set('error', 'invalid_token');
				const response = NextResponse.redirect(url);
				response.cookies.delete('auth-token');
				return response;
			}

			// Check if user is active
			if (userStatus !== 'ACTIVE') {
				console.log(`User status is ${userStatus}, redirecting to signin`);
				url.pathname = '/signin';
				url.searchParams.set('error', 'account_inactive');
				const response = NextResponse.redirect(url);
				response.cookies.delete('auth-token');
				return response;
			}

			// If user is authenticated and trying to access auth routes, redirect to app
			if (isAuthRoute) {
				console.log(
					`Authenticated user accessing ${pathname}, redirecting to /app`,
				);
				url.pathname = '/app';
				return NextResponse.redirect(url);
			}

			// Add user info to headers for the request
			const response = NextResponse.next();
			response.headers.set('x-user-email', payload.email as string);
			response.headers.set('x-user-role', userRole);
			response.headers.set('x-user-status', userStatus);

			return response;
		} catch (error) {
			console.error('JWT verification failed:', error);

			// Clear invalid token and redirect if accessing protected route
			if (isProtectedRoute) {
				console.log(
					`Invalid token for protected route ${pathname}, redirecting to signin`,
				);
				url.pathname = '/signin';
				url.searchParams.set('error', 'session_expired');
				const response = NextResponse.redirect(url);
				response.cookies.delete('auth-token');
				return response;
			}

			// For non-protected routes, just clear the invalid token and continue
			const response = NextResponse.next();
			response.cookies.delete('auth-token');
			return response;
		}
	}

	// Allow access to public routes and auth routes when not authenticated
	return NextResponse.next();
}

export const config = {
	matcher: [
		// Match all routes except API routes, static files, and assets
		'/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
		// Explicitly match app routes to ensure they're covered
		'/app/:path*',
		// Match auth routes
		'/signin',
		'/register',
	],
};
