import { useUserStore } from '@/stores/userStore';
import { NextRequest, NextResponse } from 'next/server';
import * as jose from 'jose';
export async function middleware(req: NextRequest) {
	const pathname = req.nextUrl.pathname;
	const token = useUserStore.getState().user?.accessToken.value;

	if (!token) {
		return NextResponse.redirect('/signin');
	}

	try {
		const user = await jose.jwtVerify(
			token,
			new TextEncoder().encode(process.env.NEXT_PUBLIC_JWT_SECRET),
		);
		const userRole = user.payload.role;
		const userStatus = user.payload.status;
		const isHydrated = useUserStore.getState().isHydrated;
		useUserStore.getState().setUser({
			id: user.payload.id as string,
			fullName: user.payload.fullName as string,
			email: user.payload.email as string,
			role: userRole as 'ADMIN' | 'USER',
			status: userStatus as 'ACTIVE' | 'INACTIVE' | 'REJECTED',
			accessToken: { value: token },
			refreshToken: { value: '' },
		});

		if (!isHydrated) {
			return NextResponse.redirect('/signin');
		}

		// You can add more logic here based on userRole and userStatus
	} catch (error) {
		return NextResponse.redirect('/signin');
	}
}
