import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export interface User {
	id: string;
	fullName: string;
	email: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
	accessToken: {
		value: string;
	};
	refreshToken: {
		value: string;
	};
}

interface UserState {
	user: User | null;
	isLoading: boolean;
	error: string | null;
	isHydrated: boolean;

	// Actions
	setUser: (u: User) => void;
	clearUser: () => void;
	setLoading: (loading: boolean) => void;
	setError: (msg: string | null) => void;
	setHydrated: (hydrated: boolean) => void;
}

export const useUserStore = create<UserState>()(
	persist(
		(set) => ({
			user: null,
			isLoading: false,
			error: null,
			isHydrated: false,

			setUser: (u) => set({ user: u, isLoading: false, error: null }),
			clearUser: () => set({ user: null, isLoading: false }),
			setLoading: (loading) => set({ isLoading: loading }),
			setError: (msg) => set({ error: msg, isLoading: false }),
			setHydrated: (hydrated) => set({ isHydrated: hydrated }),
		}),
		{
			name: 'user-storage',
			storage: createJSONStorage(() => localStorage),
			partialize: (state) => ({
				user: state.user,
			}),
			onRehydrateStorage: () => (state) => {
				if (state) {
					state.setHydrated(true);
				}
			},
		},
	),
);
