import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { jwtDecode } from 'jwt-decode';
import { setAuthCookie, removeAuthCookie } from '@/common/utils/cookies';

export interface User {
	id: string;
	fullName: string;
	email: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
}

interface TokenPayload {
	id: string;
	fullName: string;
	email: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
	exp: number;
	iat: number;
}

interface AuthState {
	// User data
	user: User | null;

	// Tokens
	accessToken: string | null;
	refreshToken: string | null;

	// UI state
	isLoading: boolean;
	error: string | null;
	isHydrated: boolean;

	// Actions
	setTokens: (accessToken: string, refreshToken: string) => void;
	setUser: (user: User) => void;
	logout: () => Promise<void>;
	clearError: () => void;
	setLoading: (loading: boolean) => void;
	setError: (error: string) => void;
	refreshTokens: () => Promise<boolean>;

	// Internal actions
	_setHydrated: (hydrated: boolean) => void;
}

const decodeToken = (token: string): User | null => {
	try {
		const payload = jwtDecode<TokenPayload>(token);

		// Check if token is expired
		if (payload.exp * 1000 < Date.now()) {
			return null;
		}

		return {
			id: payload.id,
			fullName: payload.fullName,
			email: payload.email,
			role: payload.role,
			status: payload.status,
		};
	} catch (error) {
		console.error('Failed to decode token:', error);
		return null;
	}
};

export const useAuthStore = create<AuthState>()(
	persist(
		(set, get) => ({
			user: null,
			accessToken: null,
			refreshToken: null,
			isLoading: false,
			error: null,
			isHydrated: false,

			setTokens: (accessToken: string, refreshToken: string) => {
				const user = decodeToken(accessToken);

				// Set cookie for middleware
				setAuthCookie(accessToken);

				set({
					accessToken,
					refreshToken,
					user,
					error: null,
					isLoading: false,
				});
			},

			setUser: (user: User) => {
				set({ user, error: null, isLoading: false });
			},

			logout: async () => {
				set({ isLoading: true });

				try {
					// Call logout API if we have a token
					const { accessToken } = get();
					if (accessToken) {
						const { AuthAPI } = await import('@/api/AuthApi');
						const authApi = new AuthAPI();
						await authApi.logout();
					}
				} catch (error) {
					console.error('Logout API call failed:', error);
					// Continue with logout even if API call fails
				}

				// Clear all auth data
				set({
					user: null,
					accessToken: null,
					refreshToken: null,
					isLoading: false,
					error: null,
				});

				// Remove auth cookie
				removeAuthCookie();

				// Redirect to signin
				if (typeof window !== 'undefined') {
					window.location.href = '/signin';
				}
			},

			clearError: () => set({ error: null }),

			setLoading: (loading: boolean) => set({ isLoading: loading }),

			setError: (error: string) => set({ error, isLoading: false }),

			refreshTokens: async (): Promise<boolean> => {
				const { refreshToken } = get();
				if (!refreshToken) return false;

				try {
					const { AuthAPI } = await import('@/api/AuthApi');
					const authApi = new AuthAPI();
					const response = await authApi.refresh();

					if (response.status === 200 && response.data?.data) {
						const {
							accessToken: newAccessToken,
							refreshToken: newRefreshToken,
						} = response.data.data;
						get().setTokens(newAccessToken, newRefreshToken);
						return true;
					}

					// Refresh failed, logout
					await get().logout();
					return false;
				} catch (error) {
					console.error('Token refresh failed:', error);
					await get().logout();
					return false;
				}
			},

			_setHydrated: (hydrated: boolean) => set({ isHydrated: hydrated }),
		}),
		{
			name: 'auth-storage',
			storage: createJSONStorage(() => localStorage),
			partialize: (state) => ({
				accessToken: state.accessToken,
				refreshToken: state.refreshToken,
				user: state.user,
			}),
			onRehydrateStorage: () => (state) => {
				if (state) {
					state._setHydrated(true);

					// Check if tokens are still valid
					if (state.accessToken) {
						const user = decodeToken(state.accessToken);
						if (!user) {
							// Token expired, clear auth data
							state.user = null;
							state.accessToken = null;
							state.refreshToken = null;
						} else {
							state.user = user;
						}
					}
				}
			},
		},
	),
);
