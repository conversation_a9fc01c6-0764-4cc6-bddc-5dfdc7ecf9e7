import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import * as jose from 'jose';
import { setAuthCookie, removeAuthCookie } from '@/common/utils/cookies';

export interface User {
	id: string;
	fullName: string;
	email: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
}

interface TokenPayload {
	email: string;
	role: 'ADMIN' | 'USER';
	status: 'ACTIVE' | 'INACTIVE' | 'REJECTED';
	type: 'auth';
	exp: number;
	iat: number;
}

interface AuthState {
	// User data
	user: User | null;

	// Tokens
	accessToken: string | null;
	refreshToken: string | null;

	// UI state
	isLoading: boolean;
	error: string | null;
	isHydrated: boolean;

	// Actions
	setTokens: (accessToken: string, refreshToken: string) => Promise<void>;
	setUser: (user: User) => void;
	logout: () => Promise<void>;
	clearError: () => void;
	setLoading: (loading: boolean) => void;
	setError: (error: string) => void;
	refreshTokens: () => Promise<boolean>;

	// Internal actions
	_setHydrated: (hydrated: boolean) => void;
	_clearAllData: () => void;
}

const decodeToken = async (
	token: string | null | undefined,
): Promise<User | null> => {
	try {
		// Check if token is valid
		if (!token || typeof token !== 'string' || token.trim() === '') {
			return null;
		}

		// Get JWT secret
		const secret =
			process.env.NEXT_PUBLIC_JWT_SECRET ||
			'pixigenerator-super-secret-jwt-key-2024';
		const secretKey = new TextEncoder().encode(secret);

		// Verify and decode the JWT
		const { payload } = await jose.jwtVerify(token, secretKey);

		// Validate payload structure
		if (
			typeof payload.email !== 'string' ||
			typeof payload.role !== 'string' ||
			typeof payload.status !== 'string' ||
			payload.type !== 'auth'
		) {
			console.error('Invalid token payload structure:', payload);
			return null;
		}

		// Type assertion for the payload (now safe after validation)
		const typedPayload = payload as unknown as TokenPayload;

		return {
			id: typedPayload.email, // Use email as ID since no separate ID in payload
			fullName: typedPayload.email.split('@')[0], // Extract name from email as fallback
			email: typedPayload.email,
			role: typedPayload.role,
			status: typedPayload.status,
		};
	} catch (error) {
		console.error('Failed to decode token:', error);
		return null;
	}
};

export const useAuthStore = create<AuthState>()(
	persist(
		(set, get) => ({
			user: null,
			accessToken: null,
			refreshToken: null,
			isLoading: false,
			error: null,
			isHydrated: false,

			setTokens: async (accessToken: string, refreshToken: string) => {
				try {
					// Validate tokens are strings
					if (!accessToken || typeof accessToken !== 'string') {
						console.error('Invalid access token:', {
							accessToken,
							type: typeof accessToken,
						});
						throw new Error('Access token must be a non-empty string');
					}

					if (!refreshToken || typeof refreshToken !== 'string') {
						console.error('Invalid refresh token:', {
							refreshToken,
							type: typeof refreshToken,
						});
						throw new Error('Refresh token must be a non-empty string');
					}

					console.log('Attempting to decode access token...');
					const user = await decodeToken(accessToken);

					if (!user) {
						throw new Error('Invalid or expired access token');
					}

					console.log('Token decoded successfully, user:', user);

					// Set cookie for middleware
					setAuthCookie(accessToken);

					set({
						accessToken,
						refreshToken,
						user,
						error: null,
						isLoading: false,
					});
				} catch (error) {
					console.error('Error setting tokens:', error);
					set({
						accessToken: null,
						refreshToken: null,
						user: null,
						error:
							error instanceof Error
								? error.message
								: 'Invalid tokens provided',
						isLoading: false,
					});
					throw error; // Re-throw so the calling component can handle it
				}
			},

			setUser: (user: User) => {
				set({ user, error: null, isLoading: false });
			},

			logout: async () => {
				set({ isLoading: true });

				try {
					// Call logout API if we have a token
					const { accessToken } = get();
					if (accessToken) {
						const { AuthAPI } = await import('@/api/AuthApi');
						const authApi = new AuthAPI();
						await authApi.logout();
					}
				} catch (error) {
					console.error('Logout API call failed:', error);
					// Continue with logout even if API call fails
				}

				// Clear all auth data
				set({
					user: null,
					accessToken: null,
					refreshToken: null,
					isLoading: false,
					error: null,
				});

				// Remove auth cookie
				removeAuthCookie();

				// Redirect to signin
				if (typeof window !== 'undefined') {
					window.location.href = '/signin';
				}
			},

			clearError: () => set({ error: null }),

			setLoading: (loading: boolean) => set({ isLoading: loading }),

			setError: (error: string) => set({ error, isLoading: false }),

			refreshTokens: async (): Promise<boolean> => {
				const { refreshToken } = get();
				if (!refreshToken) return false;

				try {
					const { AuthAPI } = await import('@/api/AuthApi');
					const authApi = new AuthAPI();
					const response = await authApi.refresh();

					if (response.status === 200 && response.data?.data) {
						const {
							accessToken: newAccessToken,
							refreshToken: newRefreshToken,
						} = response.data.data;
						await get().setTokens(newAccessToken, newRefreshToken);
						return true;
					}

					// Refresh failed, logout
					await get().logout();
					return false;
				} catch (error) {
					console.error('Token refresh failed:', error);
					await get().logout();
					return false;
				}
			},

			_setHydrated: (hydrated: boolean) => set({ isHydrated: hydrated }),

			_clearAllData: () => {
				// Clear all auth data
				set({
					user: null,
					accessToken: null,
					refreshToken: null,
					isLoading: false,
					error: null,
				});

				// Clear cookies
				removeAuthCookie();

				// Clear localStorage
				if (typeof window !== 'undefined') {
					localStorage.removeItem('auth-storage');
				}
			},
		}),
		{
			name: 'auth-storage',
			storage: createJSONStorage(() => localStorage),
			partialize: (state) => ({
				accessToken: state.accessToken,
				refreshToken: state.refreshToken,
				user: state.user,
			}),
			onRehydrateStorage: () => (state) => {
				if (state) {
					state._setHydrated(true);

					// Check if tokens are still valid
					if (state.accessToken) {
						// Use async function to handle token validation
						(async () => {
							try {
								const user = await decodeToken(state.accessToken);
								if (!user) {
									// Token expired or invalid, clear auth data
									console.log(
										'Token expired or invalid during hydration, clearing auth data',
									);
									state._clearAllData();
								} else {
									// Update the state with the validated user
									state.user = user;
								}
							} catch (error) {
								console.error(
									'Error during token validation on hydration:',
									error,
								);
								// Clear invalid data
								state._clearAllData();
							}
						})();
					}
				}
			},
		},
	),
);
