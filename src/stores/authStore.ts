import { create } from 'zustand';
import { z } from 'zod';
import { jwtDecode } from 'jwt-decode';

type Role = 'ADMIN' | 'USER';
type TokenData = { userId: string; roles: Role };

const decode = (jwt: string) => {
	const data = jwtDecode<TokenData>(jwt);
	if (!data.userId || !['ADMIN', 'USER'].includes(data.roles)) {
		throw new Error('Invalid token payload');
	}
	return data;
};

type AuthStore = {
	accessToken?: string;
	accessTokenData?: TokenData;
	refreshToken?: string;
	actions: {
		setAccessToken: (t?: string) => void;
		setRefreshToken: (t?: string) => void;
		clearTokens: () => void;
	};
};

export const useAuthStore = create<AuthStore>((set) => ({
	accessToken: undefined,
	accessTokenData: undefined,
	refreshToken: undefined,

	actions: {
		setAccessToken: (token) => {
			let data;
			try {
				data = token ? decode(token) : undefined;
			} catch {
				data = undefined;
			}
			set({ accessToken: token, accessTokenData: data });
		},
		setRefreshToken: (token) => set({ refreshToken: token }),
		clearTokens: () =>
			set({
				accessToken: undefined,
				accessTokenData: undefined,
				refreshToken: undefined,
			}),
	},
}));
