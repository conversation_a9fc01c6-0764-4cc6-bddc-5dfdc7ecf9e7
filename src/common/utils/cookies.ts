// Utility functions for managing authentication cookies

export const setAuthCookie = (token: string) => {
	if (typeof document !== 'undefined') {
		// Set cookie with secure options
		const expires = new Date();
		expires.setDate(expires.getDate() + 7); // 7 days

		const cookieString = `auth-token=${token}; expires=${expires.toUTCString()}; path=/; SameSite=Strict${
			window.location.protocol === 'https:' ? '; Secure' : ''
		}`;

		document.cookie = cookieString;
		console.log('Auth cookie set:', {
			tokenLength: token.length,
			cookieString,
		});
	}
};

export const removeAuthCookie = () => {
	if (typeof document !== 'undefined') {
		document.cookie =
			'auth-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
	}
};

export const getAuthCookie = (): string | null => {
	if (typeof document === 'undefined') return null;

	const cookies = document.cookie.split(';');
	for (const cookie of cookies) {
		const [name, value] = cookie.trim().split('=');
		if (name === 'auth-token') {
			return value;
		}
	}
	return null;
};
