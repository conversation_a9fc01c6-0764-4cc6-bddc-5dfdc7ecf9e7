import { AuthAP<PERSON> } from '@/api/AuthApi';
import { useAuthStore } from '@/stores/authStore';
import { useUserStore } from '@/stores/userStore';
import { Httpstatus } from '../StandardApi';

interface IRefreshApiResponse {
	status: number;
	data: {
		accessToken: string;
		refreshToken: string;
	};
}

class CommonFunction {
	private static instance: CommonFunction;

	public static getInstance(): CommonFunction {
		if (!CommonFunction.instance) {
			CommonFunction.instance = new CommonFunction();
		}
		return CommonFunction.instance;
	}
	static readonly createHeaders = ({
		withToken = true,
		contentType = 'application/json',
		customToken = '',
	}) => {
		const header: any = {};
		if (contentType === 'application/json') {
			header['Content-Type'] = contentType;
			header['Accept'] = contentType;
		}
		const accessToken = useAuthStore.getState().accessToken;
		if (withToken) {
			const token = customToken.length > 0 ? customToken : accessToken;
			header['Authorization'] = 'Bearer ' + token;
		}
		return header;
	};
	static readonly handleRefresh = async (): Promise<IRefreshApiResponse> => {
		try {
			const authApi = new AuthAPI();
			const response = await authApi.refresh();
			if (response.status === Httpstatus.SuccessOK) {
				const data = response?.data?.data;
				if (!data) {
					throw new Error('Invalid response data');
				}
				const { accessToken, refreshToken, user } = data;
				useUserStore.setState((state) => ({ ...state, user: user }));
				useAuthStore.setState((state) => ({
					...state,
					accessToken: accessToken,
					refreshToken: refreshToken,
				}));
			} else {
				useUserStore.setState((state) => ({ ...state, user: null }));
				useAuthStore.setState((state) => ({
					...state,
					accessToken: '',
					refreshToken: '',
				}));
			}
			return {
				status: response.status,
				data: response?.data?.data,
			};
		} catch (err) {
			console.error('Error refreshing token:', err);
			throw err;
		}
	};
	static readonly formatIsoDateToFrenshDate = (dateISO: string) => {
		const date = new Date(dateISO);

		const options: Intl.DateTimeFormatOptions = {
			year: 'numeric',
			month: 'numeric',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit',
			timeZone: 'Europe/Paris',
		};
		return date.toLocaleString('fr-FR', options);
	};
}
export { CommonFunction };
