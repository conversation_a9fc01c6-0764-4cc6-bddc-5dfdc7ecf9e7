import { IApi } from '../interface/IApi';
import { ApiResponse, HeaderObject } from '../types';
import { Httpstatus } from '../interface';
import { Method } from '../interface/EMethod';
import { CommonFunction } from '@/common/Function/Function';
import { IRefreshApiResponse } from '../interface/IRefreshResponse';

export class Api implements IApi {
	private readonly apiURL: string;
	constructor(apiURL = 'http://localhost', port?: string) {
		if (port) this.apiURL = `${apiURL}:${port}`;
		this.apiURL = `${apiURL}`;
	}

	createHeader(headerObject: HeaderObject): Headers {
		const header: Headers = new Headers();
		for (const key of Object.keys(headerObject)) {
			header.set(key, headerObject[key]);
		}
		return header;
	}

	private async standardApi(
		method: Method,
		endPoint: string,
		body: any,
		headers: any,
		refrechCallback?: () => Promise<IRefreshApiResponse>,
	): Promise<ApiResponse> {
		try {
			const response = await fetch(this.apiURL + endPoint, {
				method,
				headers,
				body,
			});
			if (refrechCallback) {
				return this.processResponse(
					response,
					(header: any) =>
						this.standardApi(method, endPoint, body, header, refrechCallback),
					refrechCallback,
					headers,
				);
			} else {
				const data = await this._getData(response);
				return {
					status: response.status,
					data: data,
				};
			}
		} catch (error: unknown) {
			throw error;
		}
	}
	private async processResponse(
		response: Response,
		callback: (headers: any) => Promise<ApiResponse>,
		refreshCallback: () => Promise<IRefreshApiResponse>,
		headers: any,
	): Promise<ApiResponse> {
		if (response.status === Httpstatus.Unauthorized) {
			try {
				const refreshResponse = await refreshCallback();
				if (refreshResponse.status === Httpstatus.SuccessOK) {
					const newHeader = CommonFunction.createHeaders({
						withToken: true,
						contentType:
							headers['Content-Type'] === 'application/json'
								? 'application/json'
								: 'form-data',
						customToken: refreshResponse?.data?.accessToken,
					});
					const res = await callback(newHeader);
					return res;
				} else {
					return {
						status: refreshResponse.status,
						data: refreshResponse?.data,
					};
				}
			} catch (error: unknown) {
				return { status: Httpstatus.Internal, data: '' };
			}
		} else {
			const data = await this._getData(response);
			return { status: response.status, data };
		}
	}

	async get(
		endPoint: string,
		headers: any,
		refrechCallback?: () => Promise<IRefreshApiResponse>,
	): Promise<ApiResponse> {
		try {
			const response = await fetch(this.apiURL + endPoint, {
				method: Method.Get,
				headers,
			});

			// Use refresh callback if provided, otherwise use default refresh
			const refreshHandler = refrechCallback || CommonFunction.handleRefresh;

			return this.processResponse(
				response,
				(header) => this.get(endPoint, header, refreshHandler),
				refreshHandler,
				headers,
			);
		} catch (error: unknown) {
			throw error;
		}
	}
	async getNotJson(
		endPoint: string,
		headers: any,
		refrechCallback?: () => Promise<IRefreshApiResponse>,
	): Promise<ApiResponse> {
		try {
			const response = await fetch(this.apiURL + endPoint, {
				method: Method.Get,
				headers,
			});
			if (refrechCallback) {
				return this.processResponse(
					response,
					(header) => this.get(endPoint, header, refrechCallback),
					refrechCallback,
					headers,
				);
			} else {
				let data = '';
				let value: Uint8Array | undefined;
				if (response.body) {
					const reader = response.body.getReader();
					const result = await reader.read();
					value = result.value;
				}
				if (value instanceof Uint8Array) {
					data += new TextDecoder().decode(value);
				} else if (typeof value === 'string') {
					data += value;
				}
				return {
					status: response.status,
					data: data,
				};
			}
		} catch (error: unknown) {
			throw error;
		}
	}
	async post(
		endPoint: string,
		body: any,
		headers: any,
		refrechCallback?: () => Promise<IRefreshApiResponse>,
	): Promise<ApiResponse> {
		try {
			const response = await fetch(this.apiURL + endPoint, {
				method: Method.Post,
				body,
				headers: headers,
			});

			// Use refresh callback if provided, otherwise use default refresh
			const refreshHandler = refrechCallback || CommonFunction.handleRefresh;

			return this.processResponse(
				response,
				(header) => this.post(endPoint, body, header, refreshHandler),
				refreshHandler,
				headers,
			);
		} catch (error: unknown) {
			throw error;
		}
	}
	async postNotJson(
		endPoint: string,
		body: any,
		headers: any,
		refrechCallback?: () => Promise<IRefreshApiResponse>,
	): Promise<ApiResponse> {
		try {
			const response = await fetch(this.apiURL + endPoint, {
				method: Method.Post,
				body,
				headers: CommonFunction.createHeaders({ withToken: true }),
			});
			if (refrechCallback) {
				return this.processResponse(
					response,
					(header) => this.post(endPoint, body, header, refrechCallback),
					refrechCallback,
					headers,
				);
			} else {
				let data = '';
				let value: Uint8Array | undefined;
				if (response.body) {
					const reader = response.body.getReader();
					const result = await reader.read();
					value = result.value;
				}
				if (value instanceof Uint8Array) {
					data += new TextDecoder().decode(value);
				} else if (typeof value === 'string') {
					data += value;
				}
				return {
					status: response.status,
					data: data,
				};
			}
		} catch (error: unknown) {
			throw error;
		}
	}
	async delete(
		endPoint: string,
		body: any,
		headers: any,
		refrechCallback?: () => Promise<IRefreshApiResponse>,
	): Promise<ApiResponse> {
		try {
			const response = await fetch(this.apiURL + endPoint, {
				method: Method.Delete,
				headers: CommonFunction.createHeaders({ withToken: true }),

				...(body && { body: body }),
			});
			if (refrechCallback) {
				return this.processResponse(
					response,
					(header) => this.delete(endPoint, body, header),
					refrechCallback,
					headers,
				);
			} else {
				const data = await this._getData(response);
				return {
					status: response.status,
					data: data,
				};
			}
		} catch (error: unknown) {
			throw error;
		}
	}
	async put(
		endPoint: string,
		body: any,
		headers: any,
		refrechCallback?: () => Promise<IRefreshApiResponse>,
	): Promise<ApiResponse> {
		return this.standardApi(
			Method.Put,
			endPoint,
			body,
			headers,
			refrechCallback,
		);
	}
	async patch(
		endPoint: string,
		body: any,
		headers: any = {},
		refrechCallback?: () => Promise<IRefreshApiResponse>,
	): Promise<ApiResponse> {
		return this.standardApi(
			Method.Patch,
			endPoint,
			body,
			headers,
			refrechCallback,
		);
	}
	private async _getData(response: Response) {
		// Read the entire body as text exactly once:
		const text = await response.text();

		// Try parsing JSON; if it fails, return the raw text:
		try {
			return JSON.parse(text);
		} catch {
			return text;
		}
	}
}
