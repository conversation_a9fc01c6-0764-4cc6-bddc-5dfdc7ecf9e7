import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';
import { useAuthStore } from '@/stores/authStore';
interface Icheck {
	email: string;
	token: string;
}
class AuthAPI {
	_api: Api;
	appOrigin: string;

	constructor(appOrigin: string = 'BO') {
		this._api = new Api(ApiURL);
		this.appOrigin = appOrigin;
	}

	async login(body: any) {
		return await this._api.post(
			`auth/signin`,
			JSON.stringify(body),
			CommonFunction.createHeaders({ withToken: false }),
		);
	}

	async register(body: any) {
		return await this._api.post(
			`auth/register`,
			JSON.stringify(body),
			CommonFunction.createHeaders({ withToken: false }),
		);
	}

	async logout() {
		return this._api.post(
			`auth/logout`,
			JSON.stringify({}),
			CommonFunction.createHeaders({
				customToken: useAuthStore.getState().accessToken,
			}),
		);
	}

	async refresh() {
		return this._api.get(
			`auth/refresh`,
			CommonFunction.createHeaders({
				customToken: useAuthStore.getState().refreshToken,
			}),
		);
	}
}
export { AuthAPI };
