import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';
import { useAuthStore } from '@/stores/authStore';

interface LoginRequest {
	email: string;
	password: string;
}

interface RegisterRequest {
	fullName: string;
	email: string;
	password: string;
}

class AuthAPI {
	_api: Api;
	appOrigin: string;

	constructor(appOrigin: string = 'BO') {
		this._api = new Api(ApiURL);
		this.appOrigin = appOrigin;
	}

	async login(body: LoginRequest) {
		return await this._api.post(
			`auth/signin`,
			JSON.stringify(body),
			CommonFunction.createHeaders({ withToken: false }),
		);
	}

	async register(body: RegisterRequest) {
		return await this._api.post(
			`auth/register`,
			JSON.stringify(body),
			CommonFunction.createHeaders({ withToken: false }),
		);
	}

	async logout() {
		const accessToken = useAuthStore.getState().accessToken;
		return this._api.post(
			`auth/logout`,
			JSON.stringify({}),
			CommonFunction.createHeaders({
				customToken: accessToken || '',
			}),
		);
	}

	async refresh() {
		const refreshToken = useAuthStore.getState().refreshToken;
		return this._api.get(
			`auth/refresh`,
			CommonFunction.createHeaders({
				customToken: refreshToken || '',
			}),
		);
	}
}
export { AuthAPI };
