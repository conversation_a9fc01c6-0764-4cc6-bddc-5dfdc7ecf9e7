import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import Link from 'next/link';

export default function TestInvitation() {
	const router = useRouter();
	const [queryParams, setQueryParams] = useState<Record<string, any>>({});

	useEffect(() => {
		if (router.isReady) {
			setQueryParams(router.query);
		}
	}, [router.isReady, router.query]);

	const testUrls = [
		'/register?token=test-invitation-token-123',
		'/register?invitation=test-invitation-token-123',
		'/register?invite=test-invitation-token-123',
	];

	return (
		<div className="min-h-screen bg-background p-8">
			<div className="max-w-2xl mx-auto">
				<h1 className="text-2xl font-bold mb-6">Invitation URL Testing</h1>
				
				<div className="mb-8">
					<h2 className="text-lg font-semibold mb-4">Current URL Parameters:</h2>
					<pre className="bg-muted p-4 rounded-lg overflow-auto">
						{JSON.stringify(queryParams, null, 2)}
					</pre>
				</div>

				<div className="mb-8">
					<h2 className="text-lg font-semibold mb-4">Test Invitation URLs:</h2>
					<div className="space-y-2">
						{testUrls.map((url, index) => (
							<div key={index}>
								<Link 
									href={url}
									className="text-blue-600 hover:underline block"
								>
									{url}
								</Link>
							</div>
						))}
					</div>
				</div>

				<div className="mb-8">
					<h2 className="text-lg font-semibold mb-4">Instructions:</h2>
					<ol className="list-decimal list-inside space-y-2 text-sm">
						<li>Click on one of the test URLs above</li>
						<li>Check the browser console for debug logs from getServerSideProps</li>
						<li>Look for messages like "Register page query parameters:" and "Extracted invitation token:"</li>
						<li>The register page should show either the invitation form or an error message</li>
					</ol>
				</div>

				<div>
					<Link 
						href="/register"
						className="inline-block bg-primary text-primary-foreground px-4 py-2 rounded hover:bg-primary/90"
					>
						Go to Register Page (no token)
					</Link>
				</div>
			</div>
		</div>
	);
}
