import type { NextPageWithLayout } from '../_app';
import Link from 'next/link';
import { RegisterForm } from '@/components/organisms';
import { AuthTemplate } from '@/components/templates';
import InvitationApi from '@/api/InvitationApi';
import { Httpstatus } from '@/common/StandardApi';
import { GetServerSideProps } from 'next';

const RegisterPage: NextPageWithLayout = (invitation: {
	token?: string;
	email?: string;
	status?: string;
}) => {
	return (
		<div className='flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8'>
			<RegisterForm invitation={invitation} />

			<div className='mt-6 text-sm text-muted-foreground'>
				<p>
					Do you have an account?{' '}
					<Link
						href='signin'
						className='font-semibold text-primary hover:underline'>
						Login
					</Link>
				</p>
			</div>
		</div>
	);
};

RegisterPage.getLayout = (page) => {
	return <AuthTemplate>{page}</AuthTemplate>;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
	const invitationToken = context.query.invitation;

	const invitationApi = new InvitationApi();
	const invitation = await invitationApi.getInvitation(
		invitationToken as string,
	);
	if (invitation.status === Httpstatus.SuccessOK) {
		return {
			props: {
				token: invitation.data.token,
				email: invitation.data.email,
				status: invitation.data.status,
			},
		};
	} else {
		return {
			props: {},
		};
	}
};
export default RegisterPage;
