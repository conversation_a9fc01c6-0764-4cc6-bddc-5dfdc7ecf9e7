import type { NextPageWithLayout } from '../_app';
import Link from 'next/link';
import { RegisterForm } from '@/components/organisms';
import { AuthTemplate } from '@/components/templates';
import InvitationApi from '@/api/InvitationApi';
import { Httpstatus } from '@/common/StandardApi';
import { GetServerSideProps } from 'next';

const RegisterPage: NextPageWithLayout = (props: {
	token?: string;
	email?: string;
	status?: string;
	error?: string;
}) => {
	const invitation = props.error
		? undefined
		: {
				token: props.token,
				email: props.email,
				status: props.status,
		  };

	return (
		<div className='flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8'>
			<RegisterForm
				invitation={invitation}
				invitationError={props.error}
			/>

			<div className='mt-6 text-sm text-muted-foreground'>
				<p>
					Do you have an account?{' '}
					<Link
						href='signin'
						className='font-semibold text-primary hover:underline'>
						Login
					</Link>
				</p>
			</div>
		</div>
	);
};

RegisterPage.getLayout = (page) => {
	return <AuthTemplate>{page}</AuthTemplate>;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
	// Check for invitation token in multiple possible query parameters
	const invitationToken =
		context.query.token || context.query.invitation || context.query.invite;

	// If no invitation token is provided, just return empty props
	if (!invitationToken || typeof invitationToken !== 'string') {
		return {
			props: {},
		};
	}

	try {
		const invitationApi = new InvitationApi();
		const invitation = await invitationApi.getInvitation(invitationToken);

		if (invitation.status === Httpstatus.SuccessOK) {
			return {
				props: {
					token: invitationToken, // Use the original token from URL
					email: invitation.data.email,
					status: invitation.data.status,
				},
			};
		} else {
			console.error('Invalid invitation token:', invitationToken);
			return {
				props: {
					error: 'Invalid or expired invitation token',
				},
			};
		}
	} catch (error) {
		console.error('Error fetching invitation:', error);
		return {
			props: {
				error: 'Failed to validate invitation token',
			},
		};
	}
};
export default RegisterPage;
