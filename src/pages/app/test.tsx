import { useAuthStore } from '@/stores/authStore';
import { useAuth } from '@/hooks/useAuth';
import { getAuthCookie } from '@/common/utils/cookies';
import { useEffect, useState } from 'react';

export default function TestPage() {
	const { user, accessToken, isLoading } = useAuthStore();
	const { logout } = useAuth();
	const [cookieValue, setCookieValue] = useState<string | null>(null);

	useEffect(() => {
		// Check cookie value on client side
		setCookieValue(getAuthCookie());
	}, []);

	if (isLoading) {
		return <div>Loading...</div>;
	}

	return (
		<div className="min-h-screen p-8 bg-background">
			<div className="max-w-2xl mx-auto">
				<h1 className="text-2xl font-bold mb-6">🔒 Protected Test Page</h1>
				
				<div className="bg-card border rounded-lg p-6 mb-6">
					<h2 className="text-lg font-semibold mb-4">Authentication Status</h2>
					
					<div className="space-y-3">
						<div className="flex justify-between">
							<span className="font-medium">User:</span>
							<span className={user ? 'text-green-600' : 'text-red-600'}>
								{user ? `${user.fullName} (${user.email})` : 'Not authenticated'}
							</span>
						</div>
						
						<div className="flex justify-between">
							<span className="font-medium">Access Token:</span>
							<span className={accessToken ? 'text-green-600' : 'text-red-600'}>
								{accessToken ? `Present (${accessToken.length} chars)` : 'Missing'}
							</span>
						</div>
						
						<div className="flex justify-between">
							<span className="font-medium">Auth Cookie:</span>
							<span className={cookieValue ? 'text-green-600' : 'text-red-600'}>
								{cookieValue ? `Present (${cookieValue.length} chars)` : 'Missing'}
							</span>
						</div>
					</div>
				</div>

				<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
					<h3 className="font-medium text-yellow-800 mb-2">🛡️ Middleware Protection</h3>
					<p className="text-sm text-yellow-700">
						If you can see this page, it means either:
					</p>
					<ul className="text-sm text-yellow-700 mt-2 ml-4 list-disc">
						<li>The middleware found a valid auth cookie and allowed access</li>
						<li>There's an issue with the middleware protection</li>
					</ul>
				</div>

				<div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
					<h3 className="font-medium text-blue-800 mb-2">🔍 Debug Info</h3>
					<p className="text-sm text-blue-700">
						Check the browser console and server logs for middleware debug information.
					</p>
				</div>

				<div className="flex gap-4">
					<button
						onClick={logout}
						className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
					>
						Logout
					</button>
					
					<button
						onClick={() => {
							setCookieValue(getAuthCookie());
						}}
						className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
					>
						Refresh Cookie Status
					</button>
				</div>
			</div>
		</div>
	);
}
