import { useAuthStore } from '@/stores/authStore';
import { useAuth } from '@/hooks/useAuth';
import { getAuthCookie, removeAuthCookie } from '@/common/utils/cookies';
import { useEffect, useState } from 'react';
import Link from 'next/link';

export default function TestAuthPage() {
	const { user, accessToken, isLoading } = useAuthStore();
	const { logout } = useAuth();
	const [cookieValue, setCookieValue] = useState<string | null>(null);

	useEffect(() => {
		setCookieValue(getAuthCookie());
	}, []);

	const clearAllAuth = () => {
		// Clear localStorage
		localStorage.removeItem('auth-storage');
		// Clear cookie
		removeAuthCookie();
		// Clear auth store
		logout();
		// Refresh cookie status
		setCookieValue(getAuthCookie());
	};

	const protectedRoutes = [
		'/app',
		'/app/test',
		'/app/projects',
	];

	return (
		<div className="min-h-screen p-8 bg-background">
			<div className="max-w-4xl mx-auto">
				<h1 className="text-3xl font-bold mb-8">🔐 Authentication Test Page</h1>
				
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
					{/* Authentication Status */}
					<div className="bg-card border rounded-lg p-6">
						<h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
						
						<div className="space-y-3">
							<div className="flex justify-between">
								<span className="font-medium">User:</span>
								<span className={user ? 'text-green-600' : 'text-red-600'}>
									{user ? `${user.fullName}` : 'Not authenticated'}
								</span>
							</div>
							
							<div className="flex justify-between">
								<span className="font-medium">Access Token:</span>
								<span className={accessToken ? 'text-green-600' : 'text-red-600'}>
									{accessToken ? 'Present' : 'Missing'}
								</span>
							</div>
							
							<div className="flex justify-between">
								<span className="font-medium">Auth Cookie:</span>
								<span className={cookieValue ? 'text-green-600' : 'text-red-600'}>
									{cookieValue ? 'Present' : 'Missing'}
								</span>
							</div>
						</div>

						<div className="mt-6 space-y-2">
							<button
								onClick={() => setCookieValue(getAuthCookie())}
								className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
							>
								Refresh Status
							</button>
							
							<button
								onClick={clearAllAuth}
								className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
							>
								Clear All Auth Data
							</button>
						</div>
					</div>

					{/* Test Protected Routes */}
					<div className="bg-card border rounded-lg p-6">
						<h2 className="text-xl font-semibold mb-4">Test Protected Routes</h2>
						<p className="text-sm text-muted-foreground mb-4">
							Click these links to test if middleware protection is working:
						</p>
						
						<div className="space-y-2">
							{protectedRoutes.map((route) => (
								<Link
									key={route}
									href={route}
									className="block w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 text-center"
								>
									Test {route}
								</Link>
							))}
						</div>

						<div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
							<p className="text-sm text-yellow-800">
								<strong>Expected behavior:</strong><br/>
								• If authenticated: Should access the page<br/>
								• If not authenticated: Should redirect to /signin
							</p>
						</div>
					</div>
				</div>

				{/* Instructions */}
				<div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
					<h3 className="text-lg font-semibold text-blue-800 mb-3">🧪 Testing Instructions</h3>
					<ol className="text-sm text-blue-700 space-y-2">
						<li><strong>1.</strong> Click "Clear All Auth Data" to logout completely</li>
						<li><strong>2.</strong> Try accessing the protected routes above</li>
						<li><strong>3.</strong> You should be redirected to /signin</li>
						<li><strong>4.</strong> Sign in and try the routes again</li>
						<li><strong>5.</strong> You should be able to access them</li>
					</ol>
					
					<div className="mt-4 p-3 bg-white border border-blue-300 rounded">
						<p className="text-sm text-blue-800">
							<strong>Check browser console and server logs</strong> for middleware debug messages.
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
