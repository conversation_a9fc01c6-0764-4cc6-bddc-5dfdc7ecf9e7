import { NextApiRequest, NextApiResponse } from 'next';
import * as jose from 'jose';

// Mock user data - replace with your actual user validation
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123', // In real app, this would be hashed
    fullName: 'Admin User',
    role: 'ADMIN' as const,
    status: 'ACTIVE' as const,
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'user123', // In real app, this would be hashed
    fullName: 'Test User',
    role: 'USER' as const,
    status: 'ACTIVE' as const,
  },
];

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ message: 'Email and password are required' });
    }

    // Find user
    const user = mockUsers.find(u => u.email === email && u.password === password);
    
    if (!user) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    // Generate JWT tokens
    const secret = process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key';
    const secretKey = new TextEncoder().encode(secret);

    const payload = {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      role: user.role,
      status: user.status,
    };

    // Create access token (expires in 1 hour)
    const accessToken = await new jose.SignJWT(payload)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('1h')
      .sign(secretKey);

    // Create refresh token (expires in 7 days)
    const refreshToken = await new jose.SignJWT({ id: user.id })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('7d')
      .sign(secretKey);

    return res.status(200).json({
      data: {
        accessToken,
        refreshToken,
        user: {
          id: user.id,
          email: user.email,
          fullName: user.fullName,
          role: user.role,
          status: user.status,
        },
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
