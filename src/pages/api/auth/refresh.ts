import { NextApiRequest, NextApiResponse } from 'next';
import * as jose from 'jose';

// Mock user data - should match signin.ts
const mockUsers = [
	{
		id: '1',
		email: '<EMAIL>',
		fullName: 'Admin User',
		role: 'ADMIN' as const,
		status: 'ACTIVE' as const,
	},
	{
		id: '2',
		email: '<EMAIL>',
		fullName: 'Test User',
		role: 'USER' as const,
		status: 'ACTIVE' as const,
	},
];

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse,
) {
	if (req.method !== 'GET') {
		return res.status(405).json({ message: 'Method not allowed' });
	}

	try {
		const authHeader = req.headers.authorization;

		if (!authHeader || !authHeader.startsWith('Bearer ')) {
			return res.status(401).json({ message: 'No refresh token provided' });
		}

		const refreshToken = authHeader.substring(7);
		const secret = process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key';
		const secretKey = new TextEncoder().encode(secret);

		// Verify refresh token
		const { payload } = await jose.jwtVerify(refreshToken, secretKey);
		const userEmail = payload.email as string;
		const tokenType = payload.type as string;

		// Check if it's a refresh token
		if (tokenType !== 'refresh') {
			return res.status(401).json({ message: 'Invalid token type' });
		}

		// Find user
		const user = mockUsers.find((u) => u.email === userEmail);

		if (!user) {
			return res.status(401).json({ message: 'User not found' });
		}

		// Generate new tokens
		const newPayload = {
			email: user.email,
			role: user.role,
			status: user.status,
			type: 'auth' as const,
		};

		// Create new access token (expires in 1 hour)
		const accessToken = await new jose.SignJWT(newPayload)
			.setProtectedHeader({ alg: 'HS256' })
			.setIssuedAt()
			.setExpirationTime('1h')
			.sign(secretKey);

		// Create new refresh token (expires in 7 days)
		const newRefreshToken = await new jose.SignJWT({
			email: user.email,
			type: 'refresh' as const,
		})
			.setProtectedHeader({ alg: 'HS256' })
			.setIssuedAt()
			.setExpirationTime('7d')
			.sign(secretKey);

		return res.status(200).json({
			data: {
				accessToken,
				refreshToken: newRefreshToken,
				user: {
					id: user.id,
					email: user.email,
					fullName: user.fullName,
					role: user.role,
					status: user.status,
				},
			},
		});
	} catch (error) {
		console.error('Refresh token error:', error);
		return res.status(401).json({ message: 'Invalid refresh token' });
	}
}
