import { NextApiRequest, NextApiResponse } from 'next';
import * as jose from 'jose';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { fullName, email, password } = req.body;

    if (!fullName || !email || !password) {
      return res.status(400).json({ message: 'Full name, email and password are required' });
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ message: 'Invalid email format' });
    }

    // Password validation
    if (password.length < 6) {
      return res.status(400).json({ message: 'Password must be at least 6 characters long' });
    }

    // In a real application, you would:
    // 1. Check if user already exists
    // 2. Hash the password
    // 3. Save to database
    // 4. Send verification email
    
    // For now, we'll just create a mock user
    const newUser = {
      id: Date.now().toString(), // Simple ID generation
      email,
      fullName,
      role: 'USER' as const,
      status: 'ACTIVE' as const,
    };

    console.log('New user registered:', { email, fullName });

    // Generate JWT tokens
    const secret = process.env.NEXT_PUBLIC_JWT_SECRET || 'your-secret-key';
    const secretKey = new TextEncoder().encode(secret);

    const payload = {
      id: newUser.id,
      email: newUser.email,
      fullName: newUser.fullName,
      role: newUser.role,
      status: newUser.status,
    };

    // Create access token (expires in 1 hour)
    const accessToken = await new jose.SignJWT(payload)
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('1h')
      .sign(secretKey);

    // Create refresh token (expires in 7 days)
    const refreshToken = await new jose.SignJWT({ id: newUser.id })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime('7d')
      .sign(secretKey);

    return res.status(201).json({
      data: {
        accessToken,
        refreshToken,
        user: {
          id: newUser.id,
          email: newUser.email,
          fullName: newUser.fullName,
          role: newUser.role,
          status: newUser.status,
        },
      },
    });
  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
