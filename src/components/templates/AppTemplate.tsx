import { Menubar } from '../organisms';

export default function AppTemplate({
	children,
}: {
	children: React.ReactNode;
}) {
	const links = [
		{
			title: 'Home',
			href: '/app',
			startIcon: null,
			endIcon: null,
			scope: 'user',
			isActive: false,
			onClick: () => {},
			className: '',
			disabled: false,
		},
		{
			title: 'Projects',
			href: '/projects',
			startIcon: null,
			endIcon: null,
			scope: 'user',
			isActive: true,
			onClick: () => {},
			className: '',
			disabled: false,
		},
	];
	return (
		<div className='flex min-h-screen flex-col bg-background text-foreground'>
			<Menubar
				logo='PixiGenerator'
				links={links}
			/>
			<main className='flex-1'>{children}</main>
		</div>
	);
}
