import { useRouter } from 'next/router';
import { useAuthStore } from '@/stores/authStore';
import { useEffect } from 'react';

export default function AuthTemplate({
	children,
}: {
	children: React.ReactNode;
}) {
	const router = useRouter();
	const { user, isHydrated } = useAuthStore();

	useEffect(() => {
		if (isHydrated && user) {
			router.replace('/app');
		}
	}, [router, user, isHydrated]);

	// Show loading while hydrating
	if (!isHydrated) {
		return (
			<div className='flex min-h-full flex-1 flex-col justify-center items-center'>
				<div>Loading...</div>
			</div>
		);
	}

	// If user is authenticated, show loading while redirecting
	if (user) {
		return (
			<div className='flex min-h-full flex-1 flex-col justify-center items-center'>
				<div>Redirecting...</div>
			</div>
		);
	}
	return (
		<div className='flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8'>
			<div className='mt-3 sm:mx-auto sm:w-full sm:max-w-sm'>{children}</div>
		</div>
	);
}
