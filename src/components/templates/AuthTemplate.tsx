import { useRouter } from 'next/router';
import { useAuthStore } from '@/stores/authStore';
import { useEffect, useState } from 'react';

export default function AuthTemplate({
	children,
}: {
	children: React.ReactNode;
}) {
	const router = useRouter();
	const { user, isHydrated } = useAuthStore();
	const [isRedirecting, setIsRedirecting] = useState(false);

	useEffect(() => {
		// Only redirect if hydrated and user exists
		if (isHydrated && user && !isRedirecting) {
			setIsRedirecting(true);
			router.replace('/app').catch(console.error);
		}
	}, [router, user, isHydrated, isRedirecting]);

	// Show loading while hydrating
	if (!isHydrated) {
		return (
			<div className='flex min-h-full flex-1 flex-col justify-center items-center'>
				<div>Loading...</div>
			</div>
		);
	}

	// If user is authenticated and redirecting, show loading
	if (isRedirecting) {
		return (
			<div className='flex min-h-full flex-1 flex-col justify-center items-center'>
				<div>Redirecting...</div>
			</div>
		);
	}

	// Otherwise, render children (the auth form)
	return <>{children}</>;
}
