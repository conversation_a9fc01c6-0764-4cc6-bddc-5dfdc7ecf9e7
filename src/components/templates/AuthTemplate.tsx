import { useRouter } from 'next/router';
import { useUserStore } from '@/stores/userStore';
import { useEffect } from 'react';

export default function AuthTemplate({
	children,
}: {
	children: React.ReactNode;
}) {
	const router = useRouter();
	const user = useUserStore((state) => state.user);
	useEffect(() => {
		if (user) {
			router.replace('/');
		}
	}, [router, user]);

	if (user) {
		return <div>Loading</div>;
	}
	return (
		<div className='flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8'>
			<div className='mt-3 sm:mx-auto sm:w-full sm:max-w-sm'>{children}</div>
		</div>
	);
}
