import { <PERSON>, Label, Fieldset, Legend } from '@headlessui/react';
import { Input, But<PERSON>, Alert } from '@/components/atoms';
import { useAuthStore } from '@/stores/authStore';
import { AuthAPI } from '@/api/AuthApi';
import { Httpstatus } from '@/common/StandardApi';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

export default function SignInForm() {
	const [error, setError] = useState<string | null>(null);
	const [message, setMessage] = useState<string | null>(null);
	const router = useRouter();
	const {
		setTokens,
		setError: setAuthError,
		isLoading,
		setLoading,
	} = useAuthStore();

	// Check for success message from registration
	useEffect(() => {
		const urlMessage = router.query.message as string;
		if (urlMessage) {
			setMessage(urlMessage);
		}
	}, [router.query.message]);

	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setLoading(true);
		setError(null);
		setAuthError('');

		try {
			const formData = new FormData(event.currentTarget);
			const email = formData.get('email') as string;
			const password = formData.get('password') as string;

			if (!email || !password) {
				setError('Please fill in all fields');
				return;
			}

			const api = new AuthAPI();
			const response = await api.login({ email, password });

			// Handle different possible success status codes
			if (
				response.status === Httpstatus.SuccessOK ||
				response.status === Httpstatus.SuccessCreated
			) {
				// Try different response structures
				let accessToken, refreshToken;

				if (response.data?.data) {
					// Structure: { data: { data: { accessToken, refreshToken } } }
					const tokenData = response.data.data;
					accessToken = tokenData.accessToken?.value || tokenData.accessToken;
					refreshToken =
						tokenData.refreshToken?.value || tokenData.refreshToken;
				} else if (response.data?.accessToken) {
					// Structure: { data: { accessToken, refreshToken } }
					accessToken =
						response.data.accessToken?.value || response.data.accessToken;
					refreshToken =
						response.data.refreshToken?.value || response.data.refreshToken;
				} else if (response.data?.token) {
					// Structure: { data: { token, refreshToken } }
					accessToken = response.data.token?.value || response.data.token;
					refreshToken =
						response.data.refreshToken?.value || response.data.refreshToken;
				} else {
					setError('Login successful but received unexpected response format.');
					return;
				}

				if (
					accessToken &&
					refreshToken &&
					typeof accessToken === 'string' &&
					typeof refreshToken === 'string'
				) {
					try {
						await setTokens(accessToken, refreshToken);
						router.push('/app');
					} catch (tokenError) {
						console.error('Error setting tokens:', tokenError);
						setError(
							'Failed to process authentication tokens. Please try again.',
						);
					}
				} else {
					setError('Login successful but tokens are invalid or missing.');
				}
			} else {
				setError(
					response.data?.message ||
						response.data?.error ||
						`Login failed (Status: ${response.status}). Please try again.`,
				);
			}
		} catch (error) {
			console.error('Login failed:', error);
			setError('An unexpected error occurred. Please try again.');
		} finally {
			setLoading(false);
		}
	};
	return (
		<div>
			{message && (
				<Alert
					className='mb-4'
					variant='default'>
					{message}
				</Alert>
			)}
			{error && (
				<Alert
					className='mb-4'
					variant='error'>
					{error}
				</Alert>
			)}
			<form
				onSubmit={onSubmit}
				className='space-y-6'>
				<Fieldset className='space-y-4'>
					<Legend className='text-lg font-semibold text-foreground'>
						Sign in to your account
					</Legend>

					<Field>
						<Label className='block text-sm/6 font-medium text-muted-foreground'>
							Email address
						</Label>

						<Input
							id='email'
							name='email'
							type='email'
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='mt-2'
						/>
					</Field>

					<Field>
						<Label className='block text-sm/6 font-medium text-muted-foreground'>
							Password
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='mt-2'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					disabled={isLoading}
					type='submit'
					className='w-full'>
					Sign in
				</Button>
			</form>
		</div>
	);
}
