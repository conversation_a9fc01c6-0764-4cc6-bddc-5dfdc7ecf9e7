import { <PERSON>, Label, Fieldset, Legend } from '@headlessui/react';
import { Input, Button, Alert } from '@/components/atoms';
import { useAuthStore } from '@/stores/authStore';
import { AuthAPI } from '@/api/AuthApi';
import { Httpstatus } from '@/common/StandardApi';
import { useState } from 'react';
import { useRouter } from 'next/router';

export default function SignInForm() {
	const [error, setError] = useState<string | null>(null);
	const [message, setMessage] = useState<string | null>(null);
	const router = useRouter();
	const {
		setTokens,
		setError: setAuthError,
		isLoading,
		setLoading,
	} = useAuthStore();

	// Check for success message from registration
	useState(() => {
		const urlMessage = router.query.message as string;
		if (urlMessage) {
			setMessage(urlMessage);
		}
	});

	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setLoading(true);
		setError(null);
		setAuthError('');

		try {
			const formData = new FormData(event.currentTarget);
			const email = formData.get('email') as string;
			const password = formData.get('password') as string;

			if (!email || !password) {
				setError('Please fill in all fields');
				return;
			}

			const api = new AuthAPI();
			const response = await api.login({ email, password });

			if (response.status === Httpstatus.SuccessOK) {
				const { accessToken, refreshToken } = response.data.data;
				setTokens(accessToken, refreshToken);

				// Redirect to app
				router.push('/app');
			} else {
				setError(response.data?.message || 'Login failed. Please try again.');
			}
		} catch (error) {
			console.error('Login failed:', error);
			setError('An unexpected error occurred. Please try again.');
		} finally {
			setLoading(false);
		}
	};
	return (
		<div>
			{message && (
				<Alert
					className='mb-4'
					variant='default'>
					{message}
				</Alert>
			)}
			{error && (
				<Alert
					className='mb-4'
					variant='error'>
					{error}
				</Alert>
			)}
			<form
				onSubmit={onSubmit}
				className='space-y-6'>
				<Fieldset className='space-y-4'>
					<Legend className='text-lg font-semibold text-foreground'>
						Sign in to your account
					</Legend>

					<Field>
						<Label className='block text-sm/6 font-medium text-muted-foreground'>
							Email address
						</Label>

						<Input
							id='email'
							name='email'
							type='email'
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='mt-2'
						/>
					</Field>

					<Field>
						<Label className='block text-sm/6 font-medium text-muted-foreground'>
							Password
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='mt-2'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					disabled={isLoading}
					type='submit'
					className='w-full'>
					Sign in
				</Button>
			</form>
		</div>
	);
}
