import { Field, Label, Description, Fieldset, Legend } from '@headlessui/react';
import { Input, Button, Alert } from '@/components/atoms';
import { useUserStore } from '@/stores/userStore';
import { AuthAPI } from '@/api/AuthApi';
import { Httpstatus } from '@/common/StandardApi';
import { useState } from 'react';
import { useAuthStore } from '@/stores/authStore';

export default function SignInForm() {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const setUser = useUserStore((state) => state.setUser);
	const setAccessToken = useAuthStore((state) => state.actions.setAccessToken);
	const setRefreshToken = useAuthStore((state) => state.actions.setRefreshToken);
	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setIsLoading(true);
		setError(null);
		try {
			const formData = new FormData(event.currentTarget);
			const email = formData.get('email') as string;
			const password = formData.get('password') as string;
			const api = new AuthAPI();
			const response = await api.login({ email, password });
			if (response.status === Httpstatus.SuccessCreated) {
				setUser(response.data);
				setAccessToken(response.data.accessToken.value);
				setRefreshToken(response.data.refreshToken.value);
			}
		} catch (error) {
			setError(error as string);
			console.error('Login failed:', error);
		} finally {
			setIsLoading(false);
		}
	};
	return (
		<div>
			{error && (
				<Alert
					className='mb-4'
					variant='error'>
					{error}
				</Alert>
			)}
			<form
				onSubmit={onSubmit}
				className='space-y-6'>
				<Fieldset className='space-y-4'>
					<Legend className='text-lg font-semibold text-foreground'>
						Sign in to your account
					</Legend>

					<Field>
						<Label className='block text-sm/6 font-medium text-muted-foreground'>
							Email address
						</Label>

						<Input
							id='email'
							name='email'
							type='email'
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='mt-2'
						/>
					</Field>

					<Field>
						<Label className='block text-sm/6 font-medium text-muted-foreground'>
							Password
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='mt-2'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					disabled={isLoading}
					type='submit'
					className='w-full'>
					Sign in
				</Button>
			</form>
		</div>
	);
}
