import { <PERSON>, Label, Fieldset, Legend } from '@headlessui/react';
import { Input, Button, Alert } from '@/components/atoms';
import { AuthAPI } from '@/api/AuthApi';
import { Httpstatus } from '@/common/StandardApi';
import { useState } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/stores/authStore';
import InvitationApi from '@/api/InvitationApi';

export default function RegisterForm({
	invitation,
	invitationError,
}: {
	invitation?: {
		token?: string;
		email?: string;
		status?: string;
	};
	invitationError?: string;
}) {
	const [error, setError] = useState<string | null>(null);
	const router = useRouter();
	const { setTokens, isLoading, setLoading } = useAuthStore();

	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setLoading(true);
		setError(null);

		try {
			const formData = new FormData(event.currentTarget);
			const fullName = formData.get('fullName') as string;
			const email = formData.get('email') as string;
			const password = formData.get('password') as string;

			if (!fullName || !email || !password) {
				setError('Please fill in all fields');
				return;
			}

			// Handle invitation acceptance if present
			if (invitation?.token) {
				const invitationApi = new InvitationApi();
				const invitationResponse = await invitationApi.acceptInvitation({
					fullName,
					email,
					password,
					token: invitation.token,
				});

				if (invitationResponse.status !== Httpstatus.SuccessAccepted) {
					setError('Invalid invitation token or invitation has expired');
					return;
				}
			}

			// Register the user
			const authApi = new AuthAPI();
			const response = await authApi.register({
				fullName,
				email,
				password,
			});

			if (response.status === Httpstatus.SuccessCreated) {
				// If registration includes tokens, set them and redirect
				if (response.data?.data?.accessToken) {
					const tokenData = response.data.data;
					const accessToken =
						tokenData.accessToken?.value || tokenData.accessToken;
					const refreshToken =
						tokenData.refreshToken?.value || tokenData.refreshToken;

					if (
						accessToken &&
						refreshToken &&
						typeof accessToken === 'string' &&
						typeof refreshToken === 'string'
					) {
						await setTokens(accessToken, refreshToken);
						router.push('/app');
					} else {
						setError('Registration successful but received invalid tokens.');
					}
				} else {
					// Otherwise redirect to signin
					router.push(
						'/signin?message=Registration successful. Please sign in.',
					);
				}
			} else {
				setError(
					response.data?.message || 'Registration failed. Please try again.',
				);
			}
		} catch (error) {
			console.error('Registration failed:', error);
			setError('An unexpected error occurred. Please try again.');
		} finally {
			setLoading(false);
		}
	};
	return (
		<div>
			{invitation?.status === 'PENDING' && (
				<Alert
					className='mb-4'
					variant='default'>
					You have been invited. Please fill in your details to create an
					account.
				</Alert>
			)}
			{invitationError && (
				<Alert
					className='mb-4'
					variant='error'>
					{invitationError}
				</Alert>
			)}
			{error && (
				<Alert
					className='mb-4'
					variant='error'>
					{error}
				</Alert>
			)}
			<form
				onSubmit={onSubmit}
				className='space-y-6'>
				<Fieldset className='space-y-4'>
					<Legend className='text-lg mb-6 font-semibold text-foreground'>
						Create an account
					</Legend>
					<Field>
						<Label
							htmlFor='fullName'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Full Name
						</Label>
						<Input
							id='fullName'
							name='fullName'
							type='text'
							required
							autoComplete='fullName'
							placeholder='John Doe'
							className='mt-2'
						/>
					</Field>
					<Field>
						<Label
							htmlFor='email'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Email address
						</Label>

						<Input
							id='email'
							name='email'
							type='email'
							defaultValue={invitation?.email || ''}
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='mt-2'
						/>
					</Field>

					<Field>
						<Label
							htmlFor='password'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Password
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='mt-2'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					disabled={isLoading}
					type='submit'
					className='w-full'>
					Register
				</Button>
			</form>
		</div>
	);
}
