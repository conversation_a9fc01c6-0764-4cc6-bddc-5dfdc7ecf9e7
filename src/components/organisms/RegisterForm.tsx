import { Field, Label, Fieldset, Legend } from '@headlessui/react';
import { Input, Button, Alert } from '@/components/atoms';
import { AuthAPI } from '@/api/AuthApi';
import { Httpstatus } from '@/common/StandardApi';
import { useState } from 'react';
import { useRouter } from 'next/router';
import { useAuthStore } from '@/stores/authStore';

export default function RegisterForm({
	invitation,
	invitationError,
	isLoadingInvitation = false,
}: {
	invitation?: {
		token?: string;
		email?: string;
		status?: string;
	};
	invitationError?: string;
	isLoadingInvitation?: boolean;
}) {
	const [error, setError] = useState<string | null>(null);
	const router = useRouter();
	const { setTokens, isLoading, setLoading } = useAuthStore();

	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setLoading(true);
		setError(null);

		try {
			const formData = new FormData(event.currentTarget);
			const fullName = formData.get('fullName') as string;
			const email = formData.get('email') as string;
			const password = formData.get('password') as string;

			if (!fullName || !email || !password) {
				setError('Please fill in all fields');
				return;
			}

			let registrationData: any = {
				fullName,
				email,
				password,
			};

			// If there's an invitation, include the token in registration
			if (invitation?.token) {
				registrationData.invitationToken = invitation.token;
			}

			// Register the user (with invitation token if present)
			const authApi = new AuthAPI();
			const response = await authApi.register(registrationData);

			if (response.status === Httpstatus.SuccessCreated) {
				// Registration successful - check if we got tokens for auto-login
				if (response.data?.data?.accessToken) {
					const tokenData = response.data.data;
					const accessToken =
						tokenData.accessToken?.value || tokenData.accessToken;
					const refreshToken =
						tokenData.refreshToken?.value || tokenData.refreshToken;

					if (
						accessToken &&
						refreshToken &&
						typeof accessToken === 'string' &&
						typeof refreshToken === 'string'
					) {
						// Auto-login the user
						await setTokens(accessToken, refreshToken);
						router.push('/app');
						return;
					}
				}

				// If no tokens returned, try to auto-login with the credentials
				try {
					const loginResponse = await authApi.login({ email, password });

					if (loginResponse.status === Httpstatus.SuccessOK) {
						const tokenData = loginResponse.data.data;
						const accessToken =
							tokenData.accessToken?.value || tokenData.accessToken;
						const refreshToken =
							tokenData.refreshToken?.value || tokenData.refreshToken;

						if (
							accessToken &&
							refreshToken &&
							typeof accessToken === 'string' &&
							typeof refreshToken === 'string'
						) {
							await setTokens(accessToken, refreshToken);
							router.push('/app');
							return;
						}
					}
				} catch (loginError) {
					console.error('Auto-login failed:', loginError);
				}

				// Fallback: redirect to signin with success message
				router.push('/signin?message=Registration successful. Please sign in.');
			} else {
				setError(
					response.data?.message || 'Registration failed. Please try again.',
				);
			}
		} catch (error) {
			console.error('Registration failed:', error);
			setError('An unexpected error occurred. Please try again.');
		} finally {
			setLoading(false);
		}
	};
	return (
		<div>
			{isLoadingInvitation && (
				<Alert
					className='mb-4'
					variant='default'>
					Validating invitation...
				</Alert>
			)}
			{!isLoadingInvitation && invitation?.status === 'PENDING' && (
				<Alert
					className='mb-4'
					variant='default'>
					You have been invited. Please fill in your details to create an
					account.
				</Alert>
			)}
			{!isLoadingInvitation && invitationError && (
				<Alert
					className='mb-4'
					variant='error'>
					{invitationError}
				</Alert>
			)}
			{error && (
				<Alert
					className='mb-4'
					variant='error'>
					{error}
				</Alert>
			)}
			<form
				onSubmit={onSubmit}
				className='space-y-6'>
				<Fieldset className='space-y-4'>
					<Legend className='text-lg mb-6 font-semibold text-foreground'>
						Create an account
					</Legend>
					<Field>
						<Label
							htmlFor='fullName'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Full Name
						</Label>
						<Input
							id='fullName'
							name='fullName'
							type='text'
							required
							autoComplete='fullName'
							placeholder='John Doe'
							className='mt-2'
						/>
					</Field>
					<Field>
						<Label
							htmlFor='email'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Email address
						</Label>

						<Input
							id='email'
							name='email'
							type='email'
							defaultValue={invitation?.email || ''}
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='mt-2'
						/>
					</Field>

					<Field>
						<Label
							htmlFor='password'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Password
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='mt-2'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					disabled={isLoading}
					type='submit'
					className='w-full'>
					Register
				</Button>
			</form>
		</div>
	);
}
