import { Field, Label, Description, Fieldset, Legend } from '@headlessui/react';
import { Input, Button, Alert } from '@/components/atoms';
import { AuthAPI } from '@/api/AuthApi';
import { Httpstatus } from '@/common/StandardApi';
import { useState } from 'react';

import InvitationApi from '@/api/InvitationApi';

export default function RegisterForm({
	invitation,
}: {
	invitation?: {
		token?: string;
		email?: string;
		status?: string;
	};
}) {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	console.log('invitation', invitation);
	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setIsLoading(true);
		setError(null);
		try {
			const formData = new FormData(event.currentTarget);
			const fullName = formData.get('fullName') as string;
			const email = formData.get('email') as string;
			const password = formData.get('password') as string;
			if (invitation) {
				const api = new InvitationApi();
				const response = await api.acceptInvitation({
					fullName,
					email,
					password: password,
					token: invitation.token,
				});
				if (response.status !== Httpstatus.SuccessAccepted) {
					setError('Invalid invitation token');
					return;
				}
			}
			const api = new AuthAPI();
			const response = await api.register({
				fullName,
				email,
				password,
			});
			if (response.status === Httpstatus.SuccessCreated)
				console.log('Registration successful:', formData);
		} catch (error) {
			setError(error as string);
			console.error('Registration failed:', error);
		} finally {
			setIsLoading(false);
		}
	};
	return (
		<div>
			{invitation?.status === 'PENDING' && (
				<Alert
					className='mb-4'
					variant='default'>
					You have been invited. Please fill in your details to create an
					account.
				</Alert>
			)}
			{error && (
				<Alert
					className='mb-4'
					variant='error'>
					{error}
				</Alert>
			)}
			<form
				onSubmit={onSubmit}
				className='space-y-6'>
				<Fieldset className='space-y-4'>
					<Legend className='text-lg mb-6 font-semibold text-foreground'>
						Create an account
					</Legend>
					<Field>
						<Label
							htmlFor='fullName'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Full Name
						</Label>
						<Input
							id='fullName'
							name='fullName'
							type='text'
							required
							autoComplete='fullName'
							placeholder='John Doe'
							className='mt-2'
						/>
					</Field>
					<Field>
						<Label
							htmlFor='email'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Email address
						</Label>

						<Input
							id='email'
							name='email'
							type='email'
							defaultValue={invitation?.email || ''}
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='mt-2'
						/>
					</Field>

					<Field>
						<Label
							htmlFor='password'
							className='block text-sm/6 font-medium text-muted-foreground'>
							Password
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='mt-2'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					disabled={isLoading}
					type='submit'
					className='w-full'>
					Register
				</Button>
			</form>
		</div>
	);
}
